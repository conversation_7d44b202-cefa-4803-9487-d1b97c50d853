package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/handlers"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/services"
)

// APIRoutes configures all API routes
type APIRoutes struct {
	serviceCoordinator *services.ServiceCoordinator
	authMiddleware     *middleware.AuthMiddleware
	authzMiddleware    *middleware.AuthorizationMiddleware
	
	// Handlers
	licenseHandler         *handlers.LicenseHandler
	machineHandler         *handlers.MachineHandler
	productHandler         *handlers.ProductHandler
	policyHandler          *handlers.PolicyHandler
	entitlementHandler     *handlers.EntitlementHandler
	groupHandler           *handlers.GroupHandler
	planHandler            *handlers.PlanHandler
	webhookEndpointHandler *handlers.WebhookEndpointHandler
	accountHandler         *handlers.AccountHandler
}

// NewAPIRoutes creates a new API routes configuration
func NewAPIRoutes(serviceCoordinator *services.ServiceCoordinator) *APIRoutes {
	// Initialize middleware
	authConfig := middleware.AuthConfig{
		PublicKey:        "", // This should be loaded from config
		TokenHeader:      "Authorization",
		TokenPrefix:      "Bearer ",
		SkipVerification: true, // For development
	}
	authMiddleware := middleware.NewAuthMiddleware(serviceCoordinator, authConfig)
	authzMiddleware := middleware.NewAuthorizationMiddleware(serviceCoordinator)

	// Initialize handlers
	licenseHandler := handlers.NewLicenseHandler(serviceCoordinator)
	machineHandler := handlers.NewMachineHandler(serviceCoordinator)
	productHandler := handlers.NewProductHandler(serviceCoordinator)
	policyHandler := handlers.NewPolicyHandler(serviceCoordinator)
	entitlementHandler := handlers.NewEntitlementHandler(serviceCoordinator)
	groupHandler := handlers.NewGroupHandler(serviceCoordinator)
	planHandler := handlers.NewPlanHandler(serviceCoordinator)
	webhookEndpointHandler := handlers.NewWebhookEndpointHandler(serviceCoordinator)
	accountHandler := handlers.NewAccountHandler(serviceCoordinator)

	return &APIRoutes{
		serviceCoordinator:     serviceCoordinator,
		authMiddleware:         authMiddleware,
		authzMiddleware:        authzMiddleware,
		licenseHandler:         licenseHandler,
		machineHandler:         machineHandler,
		productHandler:         productHandler,
		policyHandler:          policyHandler,
		entitlementHandler:     entitlementHandler,
		groupHandler:           groupHandler,
		planHandler:            planHandler,
		webhookEndpointHandler: webhookEndpointHandler,
		accountHandler:         accountHandler,
	}
}

// SetupRoutes configures all API routes
func (r *APIRoutes) SetupRoutes(router *gin.Engine) {
	// API v1 group
	api := router.Group("/api")
	v1 := api.Group("/v1")

	// Public routes (no authentication required)
	r.setupPublicRoutes(v1)

	// Protected routes (authentication required)
	r.setupProtectedRoutes(v1)

	// Admin routes (admin permissions required)
	r.setupAdminRoutes(v1)
}

// setupPublicRoutes configures public API routes
func (r *APIRoutes) setupPublicRoutes(v1 *gin.RouterGroup) {
	// License validation routes (public for license verification)
	licenses := v1.Group("/licenses")
	{
		// Basic validation endpoints (public)
		licenses.POST("/validate", r.licenseHandler.ValidatePostHandler)      // Generic validation with license key in body
		licenses.GET("/validate", r.licenseHandler.ValidateGetHandler)        // Generic validation with license key in query
		licenses.GET("/info", r.licenseHandler.InfoHandler)                   // License info lookup
		
		// Ruby API equivalent: validate by license key (with scope)
		licenses.POST("/validations", r.licenseHandler.ValidateByKeyHandler)  // Ruby: POST /licenses/validations
	}

	// Plan routes (public for billing/subscription purposes)
	plans := v1.Group("/plans")
	{
		plans.GET("", r.planHandler.ListPlansHandler)
		plans.GET("/:id", r.planHandler.GetPlanHandler)
	}

	// Health check endpoints
	health := v1.Group("/health")
	{
		health.GET("/live", r.healthLive)
		health.GET("/ready", r.healthReady)
		health.GET("/services", r.healthServices)
	}
}

// setupProtectedRoutes configures authenticated API routes
func (r *APIRoutes) setupProtectedRoutes(v1 *gin.RouterGroup) {
	// Apply authentication middleware to protected routes
	protected := v1.Group("")
	protected.Use(r.authMiddleware.RequireAuth())
	protected.Use(r.authzMiddleware.RequireAccountAccess())

	// License management routes (authenticated)
	licenses := protected.Group("/licenses")
	{
		licenses.GET("", 
			r.authMiddleware.RequirePermission(middleware.PermissionLicenseRead),
			r.licenseHandler.ListLicensesHandler)
		licenses.GET("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("license", "id", middleware.PermissionLicenseRead),
			r.licenseHandler.GetLicenseHandler)
		licenses.POST("/invalidate-cache",
			r.authMiddleware.RequirePermission(middleware.PermissionLicenseAdmin),
			r.licenseHandler.InvalidateCacheHandler)
		
		// License action endpoints (Ruby API structure)
		licenses.POST("/:id/actions/validate", r.licenseHandler.ValidateByIDHandler)           // Ruby: POST /licenses/:id/actions/validate
		licenses.GET("/:id/actions/quick-validate", r.licenseHandler.QuickValidateByIDHandler) // Ruby: GET /licenses/:id/actions/quick-validate
		
		// License checkout endpoints
		licenses.GET("/:id/actions/checkout", r.licenseHandler.CheckoutLicenseHandler)
		licenses.POST("/:id/actions/checkout", r.licenseHandler.CheckoutLicenseHandler)
	}

	// Machine management routes (authenticated)
	machines := protected.Group("/machines")
	{
		machines.GET("", 
			r.authMiddleware.RequirePermission(middleware.PermissionMachineRead),
			r.machineHandler.ListMachinesHandler)
		machines.POST("", 
			r.authMiddleware.RequirePermission(middleware.PermissionMachineWrite),
			r.machineHandler.CreateMachineHandler)
		machines.GET("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("machine", "id", middleware.PermissionMachineRead),
			r.machineHandler.GetMachineHandler)
		machines.PUT("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("machine", "id", middleware.PermissionMachineWrite),
			r.machineHandler.UpdateMachineHandler)
		machines.DELETE("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("machine", "id", middleware.PermissionMachineWrite),
			r.machineHandler.DeleteMachineHandler)
			
		// Machine heartbeat endpoints (Ruby structure)
		machines.POST("/:id/actions/heartbeats/ping", r.machineHandler.HeartbeatPingHandler)
		machines.POST("/:id/actions/heartbeats/reset", r.machineHandler.HeartbeatResetHandler)
		
		// Legacy/alternative heartbeat endpoints
		machines.POST("/:id/actions/heartbeat", r.machineHandler.HeartbeatPingHandler)
		machines.POST("/:id/actions/ping", r.machineHandler.HeartbeatPingHandler)
		machines.POST("/:id/actions/reset", r.machineHandler.HeartbeatResetHandler)
		
		// Machine checkout endpoints
		machines.GET("/:id/actions/checkout", r.machineHandler.CheckoutMachineHandler)
		machines.POST("/:id/actions/checkout", r.machineHandler.CheckoutMachineHandler)
	}

	// Product management routes
	products := protected.Group("/products")
	{
		products.GET("", 
			r.authMiddleware.RequirePermission(middleware.PermissionProductRead),
			r.productHandler.ListProductsHandler)
		products.POST("", 
			r.authMiddleware.RequirePermission(middleware.PermissionProductWrite),
			r.productHandler.CreateProductHandler)
		products.GET("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("product", "id", middleware.PermissionProductRead),
			r.productHandler.GetProductHandler)
		products.PUT("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("product", "id", middleware.PermissionProductWrite),
			r.productHandler.UpdateProductHandler)
		products.DELETE("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("product", "id", middleware.PermissionProductWrite),
			r.productHandler.DeleteProductHandler)
	}

	// Policy management routes
	policies := protected.Group("/policies")
	{
		policies.GET("", 
			r.authMiddleware.RequirePermission(middleware.PermissionPolicyRead),
			r.policyHandler.ListPoliciesHandler)
		policies.POST("", 
			r.authMiddleware.RequirePermission(middleware.PermissionPolicyWrite),
			r.policyHandler.CreatePolicyHandler)
		policies.GET("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("policy", "id", middleware.PermissionPolicyRead),
			r.policyHandler.GetPolicyHandler)
		policies.PUT("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("policy", "id", middleware.PermissionPolicyWrite),
			r.policyHandler.UpdatePolicyHandler)
		policies.DELETE("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("policy", "id", middleware.PermissionPolicyWrite),
			r.policyHandler.DeletePolicyHandler)
	}

	// Entitlement management routes
	entitlements := protected.Group("/entitlements")
	{
		entitlements.GET("", 
			r.authMiddleware.RequirePermission(middleware.PermissionEntitlementRead),
			r.entitlementHandler.ListEntitlementsHandler)
		entitlements.POST("", 
			r.authMiddleware.RequirePermission(middleware.PermissionEntitlementWrite),
			r.entitlementHandler.CreateEntitlementHandler)
		entitlements.GET("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("entitlement", "id", middleware.PermissionEntitlementRead),
			r.entitlementHandler.GetEntitlementHandler)
		entitlements.PUT("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("entitlement", "id", middleware.PermissionEntitlementWrite),
			r.entitlementHandler.UpdateEntitlementHandler)
		entitlements.DELETE("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("entitlement", "id", middleware.PermissionEntitlementWrite),
			r.entitlementHandler.DeleteEntitlementHandler)
	}

	// Group management routes
	groups := protected.Group("/groups")
	{
		groups.GET("", 
			r.authMiddleware.RequirePermission(middleware.PermissionGroupRead),
			r.groupHandler.ListGroupsHandler)
		groups.POST("", 
			r.authMiddleware.RequirePermission(middleware.PermissionGroupWrite),
			r.groupHandler.CreateGroupHandler)
		groups.GET("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("group", "id", middleware.PermissionGroupRead),
			r.groupHandler.GetGroupHandler)
		groups.PUT("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("group", "id", middleware.PermissionGroupWrite),
			r.groupHandler.UpdateGroupHandler)
		groups.DELETE("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("group", "id", middleware.PermissionGroupWrite),
			r.groupHandler.DeleteGroupHandler)
	}

	// Webhook endpoint management routes
	webhookEndpoints := protected.Group("/webhook-endpoints")
	{
		webhookEndpoints.GET("", 
			r.authMiddleware.RequirePermission(middleware.PermissionWebhookRead),
			r.webhookEndpointHandler.ListWebhookEndpointsHandler)
		webhookEndpoints.POST("", 
			r.authMiddleware.RequirePermission(middleware.PermissionWebhookWrite),
			r.webhookEndpointHandler.CreateWebhookEndpointHandler)
		webhookEndpoints.GET("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("webhook_endpoint", "id", middleware.PermissionWebhookRead),
			r.webhookEndpointHandler.GetWebhookEndpointHandler)
		webhookEndpoints.PUT("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("webhook_endpoint", "id", middleware.PermissionWebhookWrite),
			r.webhookEndpointHandler.UpdateWebhookEndpointHandler)
		webhookEndpoints.DELETE("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("webhook_endpoint", "id", middleware.PermissionWebhookWrite),
			r.webhookEndpointHandler.DeleteWebhookEndpointHandler)
	}

	// Account routes
	accounts := protected.Group("/accounts")
	{
		accounts.GET("/:account_id/info", r.accountHandler.GetAccountInfoHandler)
		accounts.PUT("/:account_id/settings", 
			r.authMiddleware.RequirePermission(middleware.PermissionAccountWrite),
			r.accountHandler.UpdateAccountSettingsHandler)
	}

	// Current account routes (shorthand)
	protected.GET("/account", r.accountHandler.GetCurrentAccountHandler)
	protected.PUT("/account", r.accountHandler.UpdateCurrentAccountHandler)

	// Account-scoped routes (Ruby Keygen standard)
	accountScoped := protected.Group("/accounts/:account_id")
	{
		// Account-scoped license routes
		accountLicenses := accountScoped.Group("/licenses")
		{
			accountLicenses.GET("", 
				r.authMiddleware.RequirePermission(middleware.PermissionLicenseRead),
				r.licenseHandler.ListLicensesHandler)
			accountLicenses.POST("", 
				r.authMiddleware.RequirePermission(middleware.PermissionLicenseWrite),
				r.licenseHandler.CreateLicenseHandler)
			accountLicenses.GET("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("license", "id", middleware.PermissionLicenseRead),
				r.licenseHandler.GetLicenseHandler)
			accountLicenses.PUT("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("license", "id", middleware.PermissionLicenseWrite),
				r.licenseHandler.UpdateLicenseHandler)
			accountLicenses.DELETE("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("license", "id", middleware.PermissionLicenseWrite),
				r.licenseHandler.DeleteLicenseHandler)
			
			// License action routes
			accountLicenses.POST("/:id/actions/validate", r.licenseHandler.ValidateByIDHandler)
			accountLicenses.GET("/:id/actions/quick-validate", r.licenseHandler.QuickValidateByIDHandler)
			accountLicenses.GET("/:id/actions/checkout", r.licenseHandler.CheckoutLicenseHandler)
			accountLicenses.POST("/:id/actions/checkout", r.licenseHandler.CheckoutLicenseHandler)
		}

		// Account-scoped machine routes
		accountMachines := accountScoped.Group("/machines")
		{
			accountMachines.GET("", 
				r.authMiddleware.RequirePermission(middleware.PermissionMachineRead),
				r.machineHandler.ListMachinesHandler)
			accountMachines.POST("", 
				r.authMiddleware.RequirePermission(middleware.PermissionMachineWrite),
				r.machineHandler.CreateMachineHandler)
			accountMachines.GET("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("machine", "id", middleware.PermissionMachineRead),
				r.machineHandler.GetMachineHandler)
			accountMachines.PUT("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("machine", "id", middleware.PermissionMachineWrite),
				r.machineHandler.UpdateMachineHandler)
			accountMachines.DELETE("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("machine", "id", middleware.PermissionMachineWrite),
				r.machineHandler.DeleteMachineHandler)
			
			// Machine action routes
			accountMachines.POST("/:id/actions/heartbeats/ping", r.machineHandler.HeartbeatPingHandler)
			accountMachines.POST("/:id/actions/heartbeats/reset", r.machineHandler.HeartbeatResetHandler)
			accountMachines.GET("/:id/actions/checkout", r.machineHandler.CheckoutMachineHandler)
			accountMachines.POST("/:id/actions/checkout", r.machineHandler.CheckoutMachineHandler)
		}

		// Account-scoped product routes
		accountProducts := accountScoped.Group("/products")
		{
			accountProducts.GET("", 
				r.authMiddleware.RequirePermission(middleware.PermissionProductRead),
				r.productHandler.ListProductsHandler)
			accountProducts.POST("", 
				r.authMiddleware.RequirePermission(middleware.PermissionProductWrite),
				r.productHandler.CreateProductHandler)
			accountProducts.GET("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("product", "id", middleware.PermissionProductRead),
				r.productHandler.GetProductHandler)
			accountProducts.PUT("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("product", "id", middleware.PermissionProductWrite),
				r.productHandler.UpdateProductHandler)
			accountProducts.DELETE("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("product", "id", middleware.PermissionProductWrite),
				r.productHandler.DeleteProductHandler)
		}

		// Account-scoped policy routes
		accountPolicies := accountScoped.Group("/policies")
		{
			accountPolicies.GET("", 
				r.authMiddleware.RequirePermission(middleware.PermissionPolicyRead),
				r.policyHandler.ListPoliciesHandler)
			accountPolicies.POST("", 
				r.authMiddleware.RequirePermission(middleware.PermissionPolicyWrite),
				r.policyHandler.CreatePolicyHandler)
			accountPolicies.GET("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("policy", "id", middleware.PermissionPolicyRead),
				r.policyHandler.GetPolicyHandler)
			accountPolicies.PUT("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("policy", "id", middleware.PermissionPolicyWrite),
				r.policyHandler.UpdatePolicyHandler)
			accountPolicies.DELETE("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("policy", "id", middleware.PermissionPolicyWrite),
				r.policyHandler.DeletePolicyHandler)
		}

		// Account-scoped entitlement routes
		accountEntitlements := accountScoped.Group("/entitlements")
		{
			accountEntitlements.GET("", 
				r.authMiddleware.RequirePermission(middleware.PermissionEntitlementRead),
				r.entitlementHandler.ListEntitlementsHandler)
			accountEntitlements.POST("", 
				r.authMiddleware.RequirePermission(middleware.PermissionEntitlementWrite),
				r.entitlementHandler.CreateEntitlementHandler)
			accountEntitlements.GET("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("entitlement", "id", middleware.PermissionEntitlementRead),
				r.entitlementHandler.GetEntitlementHandler)
			accountEntitlements.PUT("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("entitlement", "id", middleware.PermissionEntitlementWrite),
				r.entitlementHandler.UpdateEntitlementHandler)
			accountEntitlements.DELETE("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("entitlement", "id", middleware.PermissionEntitlementWrite),
				r.entitlementHandler.DeleteEntitlementHandler)
		}

		// Account-scoped group routes
		accountGroups := accountScoped.Group("/groups")
		{
			accountGroups.GET("", 
				r.authMiddleware.RequirePermission(middleware.PermissionGroupRead),
				r.groupHandler.ListGroupsHandler)
			accountGroups.POST("", 
				r.authMiddleware.RequirePermission(middleware.PermissionGroupWrite),
				r.groupHandler.CreateGroupHandler)
			accountGroups.GET("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("group", "id", middleware.PermissionGroupRead),
				r.groupHandler.GetGroupHandler)
			accountGroups.PUT("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("group", "id", middleware.PermissionGroupWrite),
				r.groupHandler.UpdateGroupHandler)
			accountGroups.DELETE("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("group", "id", middleware.PermissionGroupWrite),
				r.groupHandler.DeleteGroupHandler)
		}

		// Account-scoped webhook endpoint routes
		accountWebhooks := accountScoped.Group("/webhook-endpoints")
		{
			accountWebhooks.GET("", 
				r.authMiddleware.RequirePermission(middleware.PermissionWebhookRead),
				r.webhookEndpointHandler.ListWebhookEndpointsHandler)
			accountWebhooks.POST("", 
				r.authMiddleware.RequirePermission(middleware.PermissionWebhookWrite),
				r.webhookEndpointHandler.CreateWebhookEndpointHandler)
			accountWebhooks.GET("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("webhook_endpoint", "id", middleware.PermissionWebhookRead),
				r.webhookEndpointHandler.GetWebhookEndpointHandler)
			accountWebhooks.PUT("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("webhook_endpoint", "id", middleware.PermissionWebhookWrite),
				r.webhookEndpointHandler.UpdateWebhookEndpointHandler)
			accountWebhooks.DELETE("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("webhook_endpoint", "id", middleware.PermissionWebhookWrite),
				r.webhookEndpointHandler.DeleteWebhookEndpointHandler)
		}

		// Account-scoped user routes
		accountUsers := accountScoped.Group("/users")
		{
			accountUsers.GET("", 
				r.authMiddleware.RequirePermission(middleware.PermissionUserRead),
				r.listUsers)
			accountUsers.POST("", 
				r.authMiddleware.RequirePermission(middleware.PermissionUserWrite),
				r.createUser)
			accountUsers.GET("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("user", "id", middleware.PermissionUserRead),
				r.getUser)
			accountUsers.PUT("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("user", "id", middleware.PermissionUserWrite),
				r.updateUser)
			accountUsers.DELETE("/:id", 
				r.authzMiddleware.RequireOwnershipOrPermission("user", "id", middleware.PermissionUserWrite),
				r.deleteUser)
		}
	}

	// User routes
	users := protected.Group("/users")
	{
		users.GET("/me", r.getCurrentUser)
		users.PUT("/me", r.updateCurrentUser)
		users.GET("", 
			r.authMiddleware.RequirePermission(middleware.PermissionUserRead),
			r.listUsers)
		users.GET("/:id", 
			r.authzMiddleware.RequireOwnershipOrPermission("user", "id", middleware.PermissionUserRead),
			r.getUser)
	}
}

// setupAdminRoutes configures admin-only API routes
func (r *APIRoutes) setupAdminRoutes(v1 *gin.RouterGroup) {
	admin := v1.Group("/admin")
	admin.Use(r.authMiddleware.RequireAuth())
	admin.Use(r.authMiddleware.RequirePermission(middleware.PermissionSystemAdmin))

	// System administration
	admin.GET("/stats", r.getSystemStats)
	admin.GET("/health/detailed", r.getDetailedHealth)
	admin.POST("/cache/clear", r.clearCache)
	admin.GET("/accounts", r.accountHandler.ListAccountsHandler)
	admin.GET("/accounts/:id", r.accountHandler.GetAccountDetailsHandler)
}

// Health check handlers

func (r *APIRoutes) healthLive(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":    "alive",
		"timestamp": "2024-01-01T00:00:00Z", // This would be real timestamp
		"service":   "gokeys-api",
	})
}

func (r *APIRoutes) healthReady(c *gin.Context) {
	health := r.serviceCoordinator.Health()
	
	status := 200
	overallStatus := "ready"
	
	// Check if any service is unhealthy
	for _, serviceHealth := range health {
		if healthMap, ok := serviceHealth.(map[string]interface{}); ok {
			if healthStatus, ok := healthMap["status"].(string); ok && healthStatus == "unhealthy" {
				status = 503
				overallStatus = "not_ready"
				break
			}
		}
	}

	response := gin.H{
		"status":    overallStatus,
		"timestamp": "2024-01-01T00:00:00Z", // This would be real timestamp
		"checks":    health,
	}

	c.JSON(status, response)
}

func (r *APIRoutes) healthServices(c *gin.Context) {
	serviceInfo := r.serviceCoordinator.GetServiceInfo()
	c.JSON(200, gin.H{
		"status":   "ok",
		"services": serviceInfo,
	})
}

// Placeholder handlers for routes that haven't been fully implemented

func (r *APIRoutes) getCurrentUser(c *gin.Context) {
	userID, _ := middleware.GetUserID(c)
	accountID, _ := middleware.GetAccountID(c)
	
	c.JSON(200, gin.H{
		"message": "Get current user endpoint - implementation pending",
		"user_id": userID,
		"account_id": accountID,
	})
}

func (r *APIRoutes) updateCurrentUser(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "Update current user endpoint - implementation pending",
	})
}

func (r *APIRoutes) listUsers(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "List users endpoint - implementation pending",
	})
}

func (r *APIRoutes) getUser(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "Get user endpoint - implementation pending",
		"user_id": c.Param("id"),
	})
}

func (r *APIRoutes) getSystemStats(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "System stats endpoint - implementation pending",
	})
}

func (r *APIRoutes) getDetailedHealth(c *gin.Context) {
	health := r.serviceCoordinator.Health()
	c.JSON(200, gin.H{
		"status": "ok",
		"detailed_health": health,
		"timestamp": "2024-01-01T00:00:00Z",
	})
}

func (r *APIRoutes) clearCache(c *gin.Context) {
	// Note: Cache interface doesn't have Clear method
	// This would need to be implemented per cache type
	c.JSON(200, gin.H{
		"message": "Cache clear not implemented",
	})
}

func (r *APIRoutes) createUser(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "Create user endpoint - implementation pending",
	})
}

func (r *APIRoutes) updateUser(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "Update user endpoint - implementation pending",
		"user_id": c.Param("id"),
	})
}

func (r *APIRoutes) deleteUser(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "Delete user endpoint - implementation pending",
		"user_id": c.Param("id"),
	})
}

